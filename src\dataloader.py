import pandas as pd
from pathlib import Path

# --------------------------
# 1. INTEGRAÇÃO
# --------------------------

def _resolve_first_existing_path(candidates):
    base_dir = Path(__file__).resolve().parents[1]
    data_dir = base_dir / "notebooks" / "Dados"
    for candidate in candidates:
        candidate_path = data_dir / candidate
        if candidate_path.exists():
            return candidate_path
    raise FileNotFoundError(
        f"Nenhum dos arquivos foi encontrado em {data_dir}: {candidates}"
    )

# Carregar arquivos
cte_train = pd.read_csv(_resolve_first_existing_path(["CTE-24-25(in).csv"]), sep=";")
cte_test  = pd.read_csv(_resolve_first_existing_path(["CTE-22-23-24(in).csv"]), sep=";")
graneis_train = pd.read_csv(_resolve_first_existing_path([
    "Graneis-24-25(in) (1).csv",
    "Graneis-24-25(in).csv",
]), sep=";")
graneis_test  = pd.read_csv(_resolve_first_existing_path(["Graneis-22-23-24(in).csv"]), sep=";")

# Filtrar produtos válidos
cte_train = cte_train[cte_train["Produto"].fillna("").str.upper().str.contains("GESSO|CALCARIO", na=False)]
cte_test  = cte_test[cte_test["Produto"].fillna("").str.upper().str.contains("GESSO|CALCARIO", na=False)]
graneis_train = graneis_train[graneis_train["Produto"].fillna("").str.upper().str.contains("GESSO|CALCARIO", na=False)]
graneis_test  = graneis_test[graneis_test["Produto"].fillna("").str.upper().str.contains("GESSO|CALCARIO", na=False)]

# --------------------------
# 1a. DESDUPLICAR CTE (mantendo Graneis completo)
# --------------------------

cte_train_unique = cte_train.drop_duplicates(subset=["Chave de Acesso"], keep="first")
cte_test_unique  = cte_test.drop_duplicates(subset=["Chave de Acesso"], keep="first")

# Merge -> Graneis como base, enriquecido com infos de CTE únicas
train_data = pd.merge(graneis_train, cte_train_unique, on="Chave de Acesso", how="left", suffixes=("_graneis", "_cte"))
test_data  = pd.merge(graneis_test, cte_test_unique, on="Chave de Acesso", how="left", suffixes=("_graneis", "_cte"))

# --------------------------
# 1b. REMOVER COLUNAS DUPLICADAS DE PRODUTO E CHAVE DE ACESSO
# --------------------------

cols_para_remover = ["Produto_cte", "Chave de Acesso_cte"]
train_data = train_data.drop(columns=[c for c in cols_para_remover if c in train_data.columns])
test_data  = test_data.drop(columns=[c for c in cols_para_remover if c in test_data.columns])

# --------------------------
# 2. LIMPEZA DE DADOS
# --------------------------

def limpar_dataset(df):
    if "Data de Emissão" in df.columns:
        df["Data de Emissão"] = pd.to_datetime(df["Data de Emissão"], errors="coerce", dayfirst=True)

    for col in df.select_dtypes(include="object"):
        if df[col].str.contains(",", regex=False).any():
            df[col] = df[col].str.replace(".", "", regex=False)
            df[col] = df[col].str.replace(",", ".", regex=False)
            try:
                df[col] = pd.to_numeric(df[col])
            except:
                pass

    df = df.drop_duplicates()
    df = df.dropna(subset=["Chave de Acesso"])
    df = df.fillna(0)
    return df

train_data = limpar_dataset(train_data)
test_data  = limpar_dataset(test_data)

# --------------------------
# 3. FEATURE ENGINEERING
# --------------------------

def criar_features(df):
    if "Data de Emissão" in df.columns:
        df["Ano"] = df["Data de Emissão"].dt.year
        df["Mes"] = df["Data de Emissão"].dt.month
        df["Dia"] = df["Data de Emissão"].dt.day
        df["DiaSemana"] = df["Data de Emissão"].dt.dayofweek
        df["Safra"] = df["Ano"]
        df.loc[df["Mes"] < 7, "Safra"] = df["Ano"] - 1

    if "UF Origem" in df.columns:
        dummies = pd.get_dummies(df["UF Origem"], prefix="UFOrigem")
        df = pd.concat([df, dummies], axis=1)
    return df

train_data = criar_features(train_data)
test_data  = criar_features(test_data)

# --------------------------
# RESUMO FINAL
# --------------------------
print("\nTreino final:", train_data.shape)
print(train_data.head())
print(train_data.columns)

print("\nTeste final:", test_data.shape)
print(test_data.head())
print(test_data.columns)

# --------------------------
# 4. EXPLORAÇÃO DE DADOS
# --------------------------

# 1️⃣ Identificar colunas numéricas e categóricas
colunas_numericas = train_data.select_dtypes(include=['int64', 'float64']).columns
colunas_categoricas = train_data.select_dtypes(include=['object', 'category']).columns

print("\nColunas numéricas:", colunas_numericas)
print("Colunas categóricas:", colunas_categoricas)

# 2️⃣ Estatísticas descritivas

print("\n--- Estatísticas descritivas (numéricas) ---")
print(train_data[colunas_numericas].describe())

print("\n--- Estatísticas descritivas (categóricas) ---")
for col in colunas_categoricas:
    print(f"\nColuna: {col}")
    print(train_data[col].describe())
    print("Valores mais frequentes:\n", train_data[col].value_counts().head(10))

# 3️⃣ Verificar valores nulos
print("\nValores nulos por coluna:")
print(train_data.isnull().sum())
