import pandas as pd
from pathlib import Path

def _resolve_first_existing_path(candidates):
    base_dir = Path(__file__).resolve().parents[1]
    data_dir = base_dir / "notebooks" / "Dados"
    for candidate in candidates:
        candidate_path = data_dir / candidate
        if candidate_path.exists():
            return candidate_path
    raise FileNotFoundError(
        f"Nenhum dos arquivos foi encontrado em {data_dir}: {candidates}"
    )


# === Carregar CTEs ===
cte_train_path = _resolve_first_existing_path([
    "CTE-24-25(in).csv",
])
cte_test_path = _resolve_first_existing_path([
    "CTE-22-23-24(in).csv",
])

# === Carr<PERSON>ar <PERSON> (corrigindo separador) ===
graneis_train_path = _resolve_first_existing_path([
    "Graneis-24-25(in) (1).csv",
    "Graneis-24-25(in).csv",
])
graneis_test_path = _resolve_first_existing_path([
    "Graneis-22-23-24(in).csv",
])

cte_train = pd.read_csv(cte_train_path, sep=";")
cte_test = pd.read_csv(cte_test_path, sep=";")
graneis_train = pd.read_csv(graneis_train_path, sep=";")
graneis_test = pd.read_csv(graneis_test_path, sep=";")

# === Filtrar apenas produtos de interesse ===
produtos_validos = ["GESSO", "GESSO AGRICOLA", "CALCARIO", "CALCARIO DOLOMITICO"]

cte_train = cte_train[cte_train["Produto"].fillna("").str.upper().str.contains("GESSO|CALCARIO", na=False)]
cte_test  = cte_test[cte_test["Produto"].fillna("").str.upper().str.contains("GESSO|CALCARIO", na=False)]

graneis_train = graneis_train[graneis_train["Produto"].fillna("").str.upper().str.contains("GESSO|CALCARIO", na=False)]
graneis_test  = graneis_test[graneis_test["Produto"].fillna("").str.upper().str.contains("GESSO|CALCARIO", na=False)]

# === Merge usando chave de acesso ===
train_data = pd.merge(cte_train, graneis_train, on="Chave de Acesso", how="inner")
test_data  = pd.merge(cte_test, graneis_test, on="Chave de Acesso", how="inner")

print("Treino:", train_data.shape)
print("Teste:", test_data.shape)

# Exemplo: separar features e target
# Supondo que "Valor CT-e" seja o valor do frete a ser previsto
X_train = train_data.drop(columns=["Valor CT-e"])
y_train = train_data["Valor CT-e"]

X_test = test_data.drop(columns=["Valor CT-e"])
y_test = test_data["Valor CT-e"]
