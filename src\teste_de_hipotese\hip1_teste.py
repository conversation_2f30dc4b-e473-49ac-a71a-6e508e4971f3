import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import statsmodels.formula.api as smf
from statsmodels.stats.anova import anova_lm
from statsmodels.stats.diagnostic import het_white
from statsmodels.stats.stattools import durbin_watson

df = pd.read_csv("notebooks/Dados/Graneis-22-23-24(in).csv", sep=";")
modelo = smf.ols("preco_frete ~ valor_imposto + C(uf_destino)", data=df_teste_clean).fit()

df_teste = df.rename(columns={
    'Valor Total da Prestação do Serviço': 'preco_frete',
    'Total Imposto': 'valor_imposto', 
    'UF do fim do transporte': 'uf_destino'
})

df_teste = df_teste.dropna(subset=['preco_frete', 'valor_imposto', 'uf_destino'])

print("TESTE DE HIPÓTESE: IMPACTO DA UF NO PREÇO DO FRETE")
print(f"Amostra: {len(df_teste)} observações")
print(f"UFs únicas: {df_teste['uf_destino'].nunique()}")

print(f"Número de UFs únicas: {df_teste['uf_destino'].nunique()}")
print(f"Tamanho do dataset: {len(df_teste)}")

uf_counts = df_teste['uf_destino'].value_counts()
ufs_relevantes = uf_counts[uf_counts >= 100].index
df_teste_filtrado = df_teste[df_teste['uf_destino'].isin(ufs_relevantes)]

print(f"UFs com >= 100 observações: {len(ufs_relevantes)}")
print(f"Dataset filtrado: {len(df_teste_filtrado)} observações")

if len(df_teste_filtrado) > 50000:
    df_teste_sample = df_teste_filtrado.sample(n=50000, random_state=42)
    print(f"Usando amostra de {len(df_teste_sample)} observações")
else:
    df_teste_sample = df_teste_filtrado

df_teste_clean = df_teste_sample.copy()
df_teste_clean['preco_frete'] = pd.to_numeric(df_teste_clean['preco_frete'], errors='coerce')
df_teste_clean['valor_imposto'] = pd.to_numeric(df_teste_clean['valor_imposto'], errors='coerce')

df_teste_clean = df_teste_clean.dropna(subset=['preco_frete', 'valor_imposto'])
df_teste_clean['uf_destino'] = df_teste_clean['uf_destino'].astype(str)

print(f"Dataset após limpeza: {len(df_teste_clean)} observações")
print("Tipos após conversão:")
print(df_teste_clean[['preco_frete', 'valor_imposto', 'uf_destino']].dtypes)

print(f"\n COEFICIENTE DO IMPOSTO:")
coef_imposto = modelo.params['valor_imposto']
pvalue_imposto = modelo.pvalues['valor_imposto']
print(f"   Coeficiente: {coef_imposto:.4f}")
print(f"   P-value: {pvalue_imposto:.4f}")

if pvalue_imposto < 0.05:
    print(f"    Imposto tem efeito significativo no frete")
else:
    print(f"    Imposto NÃO tem efeito significativo no frete")

if modelo.f_pvalue < 0.05:
    print(f"\n RESULTADO: O modelo é estatisticamente significativo (p < 0.05)")
    print(f"   Rejeitamos H₀: Existe diferença significativa no frete entre UFs")
else:
    print(f"\n RESULTADO: O modelo NÃO é estatisticamente significativo (p >= 0.05)")
    print(f"   Não rejeitamos H₀: Não há evidência de diferença entre UFs")
